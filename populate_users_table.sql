-- S<PERSON> Script to populate your users table with data from Supabase Auth
-- Run this in your Supabase SQL Editor

-- First, let's see the current structure of your users table
-- SELECT * FROM users;

-- Insert users based on your Supabase Auth users
-- Note: The users table has numeric IDs, but we'll use email as the primary identifier

-- Insert Abdo
INSERT INTO users (id, name, email, role) 
VALUES (1, 'Abdo Do', '<EMAIL>', 'user')
ON CONFLICT (email) DO UPDATE SET 
  name = EXCLUDED.name,
  role = EXCLUDED.role;

-- Insert Sohaib
INSERT INTO users (id, name, email, role) 
VALUES (2, 'Sohaib Bkt', '<EMAIL>', 'user')
ON CONFLICT (email) DO UPDATE SET 
  name = EXCLUDED.name,
  role = EXCLUDED.role;

-- Verify the data was inserted
SELECT * FROM users;

-- Optional: Create a function to automatically sync Supabase Auth users to your users table
-- This would be triggered whenever a new user signs up

CREATE OR REPLACE FUNCTION sync_auth_user_to_users_table()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (name, email, role)
  VALUES (
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
    NEW.email,
    'user'
  )
  ON CONFLICT (email) DO UPDATE SET
    name = COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
    role = 'user';
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically sync new auth users
-- DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
-- CREATE TRIGGER on_auth_user_created
--   AFTER INSERT ON auth.users
--   FOR EACH ROW EXECUTE FUNCTION sync_auth_user_to_users_table();

-- Note: The trigger is commented out because it requires admin privileges
-- You can enable it if you have the necessary permissions
