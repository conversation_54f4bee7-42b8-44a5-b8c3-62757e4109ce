import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UserPlus, Copy } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface JoinProjectDialogProps {
  onProjectJoined: () => void;
}

const JoinProjectDialog: React.FC<JoinProjectDialogProps> = ({ onProjectJoined }) => {
  const [open, setOpen] = useState(false);
  const [accessCode, setAccessCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleJoinProject = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!accessCode.trim()) {
      setError('Please enter an access code');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Call the database function to join project
      const { data, error } = await supabase.rpc('join_project_by_code', {
        access_code_param: accessCode.trim().toUpperCase()
      });

      if (error) {
        setError(error.message);
      } else if (data && data.success) {
        setSuccess(`Successfully joined project: ${data.project_name}`);
        setAccessCode('');
        onProjectJoined();
        setTimeout(() => {
          setOpen(false);
          setSuccess('');
        }, 2000);
      } else {
        setError(data?.message || 'Failed to join project');
      }
    } catch (err) {
      console.error('Error joining project:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setOpen(false);
    setAccessCode('');
    setError('');
    setSuccess('');
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <UserPlus className="mr-2 h-4 w-4" />
          Join Project
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Join Project</DialogTitle>
          <DialogDescription>
            Enter the access code shared by the project owner to join their project.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleJoinProject} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="border-green-200 bg-green-50">
              <AlertDescription className="text-green-800">{success}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="accessCode">Access Code</Label>
            <Input
              id="accessCode"
              value={accessCode}
              onChange={(e) => setAccessCode(e.target.value.toUpperCase())}
              placeholder="Enter 8-character code (e.g., ABC12345)"
              maxLength={8}
              className="font-mono text-center text-lg tracking-wider"
              required
            />
            <p className="text-xs text-gray-500">
              Access codes are 8 characters long and case-insensitive
            </p>
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Joining...
                </div>
              ) : (
                <div className="flex items-center">
                  <UserPlus className="mr-2 h-4 w-4" />
                  Join Project
                </div>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default JoinProjectDialog;
