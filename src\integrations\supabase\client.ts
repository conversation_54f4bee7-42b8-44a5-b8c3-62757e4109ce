// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://uqnfarbmdrysdowtzzcm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVxbmZhcmJtZHJ5c2Rvd3R6emNtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjExNDcsImV4cCI6MjA2NDEzNzE0N30.Qb5ke3hxW8qZS-sbx_7asJxAs3IZINClj6Bkaq-jO6M";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);