
import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, MoreHorizontal, Calendar, User, Edit, Trash, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import CreateTaskDialog from '../components/CreateTaskDialog';
import EditTaskDialog from '../components/EditTaskDialog';
import { supabase } from '@/integrations/supabase/client';

interface Task {
  id: string;
  title: string;
  description: string;
  status: 'To Do' | 'In Progress' | 'Done';
  priority: 'Low' | 'Medium' | 'High';
  assignee: string;
  project: string;
  dueDate: string;
  createdAt: string;
  // Database fields (for mapping)
  assigned_to?: number | null;
  due_date?: string | null;
  project_id?: number | null;
  created_at?: string;
}

const Tasks = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);

  // Test connection function
  const testConnection = async () => {
    try {
      console.log('Testing tasks table connection...');
      const { data, error } = await supabase.from('tasks').select('*');

      if (error) {
        console.error('Tasks connection test failed:', error);
        alert(`Database error: ${error.message}\nDetails: ${error.details || 'No details'}`);
      } else {
        console.log('Tasks connection successful!', data);
        alert(`Tasks table connection successful!\nFound ${data?.length || 0} records`);
      }
    } catch (err) {
      console.error('Tasks connection test exception:', err);
      alert(`Connection error: ${err}`);
    }
  };

  // Create test data function
  const createTestData = async () => {
    try {
      console.log('Creating test tasks...');
      const testTasks = [
        {
          title: 'Design homepage wireframes',
          description: 'Create detailed wireframes for the new homepage layout',
          status: 'In Progress',
          due_date: '2024-05-25T00:00:00Z',
          project_id: 3,
          assigned_to: null
        },
        {
          title: 'Set up authentication system',
          description: 'Implement user login and registration functionality',
          status: 'To Do',
          due_date: '2024-05-26T00:00:00Z',
          project_id: null,
          assigned_to: null
        },
        {
          title: 'Write marketing copy',
          description: 'Create compelling copy for the marketing campaign',
          status: 'Done',
          due_date: '2024-05-24T00:00:00Z',
          project_id: null,
          assigned_to: null
        }
      ];

      const { data, error } = await supabase
        .from('tasks')
        .insert(testTasks)
        .select();

      if (error) {
        console.error('Error creating test tasks:', error);
        alert(`Error creating test tasks: ${error.message}`);
      } else {
        console.log('Test tasks created successfully:', data);
        alert(`Successfully created ${data.length} test tasks!`);
        // Refresh the tasks list
        const fetchedTasks = await fetchTasks();
        setTasks(fetchedTasks);
      }
    } catch (err) {
      console.error('Exception creating test tasks:', err);
      alert(`Error creating test tasks: ${err}`);
    }
  };

  // Fetch tasks from database
  const fetchTasks = async () => {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('id, title, description, status, due_date, project_id, assigned_to, created_at');

      if (error) {
        console.error('Error fetching tasks:', error);
        return [];
      }

      console.log('Raw tasks data from Supabase:', data);

      if (!data) {
        console.log('No tasks data returned from Supabase');
        return [];
      }

      // Map database fields to frontend interface
      return data.map(task => ({
        id: task.id.toString(),
        title: task.title || '',
        description: task.description || '',
        status: (task.status as 'To Do' | 'In Progress' | 'Done') || 'To Do',
        priority: 'Medium' as 'Low' | 'Medium' | 'High', // Default priority since not in DB
        assignee: task.assigned_to ? `User${task.assigned_to}` : 'Unassigned',
        project: task.project_id ? `Project${task.project_id}` : 'No Project',
        dueDate: task.due_date || new Date().toISOString(),
        createdAt: task.created_at || new Date().toISOString(),
      }));
    } catch (err) {
      console.error('Exception in fetchTasks:', err);
      return [];
    }
  };

  // Load tasks on component mount
  useEffect(() => {
    const loadTasks = async () => {
      const fetchedTasks = await fetchTasks();
      console.log('Fetched tasks:', fetchedTasks);
      setTasks(fetchedTasks);
    };

    loadTasks();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'To Do':
        return 'bg-gray-100 text-gray-800';
      case 'In Progress':
        return 'bg-blue-100 text-blue-800';
      case 'Done':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High':
        return 'bg-red-100 text-red-800';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (task.description && task.description.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = statusFilter === 'All' || task.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleCreateTask = async (newTask: Omit<Task, 'id' | 'createdAt'>) => {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .insert({
          title: newTask.title,
          description: newTask.description,
          status: newTask.status,
          due_date: newTask.dueDate,
          project_id: null, // Will be updated when projects are linked
          assigned_to: null, // Will be updated when users are linked
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating task:', error);
        alert(`Error creating task: ${error.message}`);
        return;
      }

      // Refresh the tasks list
      const fetchedTasks = await fetchTasks();
      setTasks(fetchedTasks);
    } catch (error) {
      console.error('Error creating task:', error);
      alert(`Error creating task: ${error}`);
    }
  };

  const handleEditTask = async (updatedTask: Task) => {
    try {
      const { error } = await supabase
        .from('tasks')
        .update({
          title: updatedTask.title,
          description: updatedTask.description,
          status: updatedTask.status,
          due_date: updatedTask.dueDate,
        })
        .eq('id', parseInt(updatedTask.id));

      if (error) {
        console.error('Error updating task:', error);
        alert(`Error updating task: ${error.message}`);
        return;
      }

      // Refresh the tasks list
      const fetchedTasks = await fetchTasks();
      setTasks(fetchedTasks);
      setSelectedTask(null);
    } catch (error) {
      console.error('Error updating task:', error);
      alert(`Error updating task: ${error}`);
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', parseInt(taskId));

      if (error) {
        console.error('Error deleting task:', error);
        alert(`Error deleting task: ${error.message}`);
        return;
      }

      // Refresh the tasks list
      const fetchedTasks = await fetchTasks();
      setTasks(fetchedTasks);
    } catch (error) {
      console.error('Error deleting task:', error);
      alert(`Error deleting task: ${error}`);
    }
  };

  const handleStatusChange = async (taskId: string, newStatus: Task['status']) => {
    try {
      const { error } = await supabase
        .from('tasks')
        .update({ status: newStatus })
        .eq('id', parseInt(taskId));

      if (error) {
        console.error('Error updating task status:', error);
        alert(`Error updating task status: ${error.message}`);
        return;
      }

      // Refresh the tasks list
      const fetchedTasks = await fetchTasks();
      setTasks(fetchedTasks);
    } catch (error) {
      console.error('Error updating task status:', error);
      alert(`Error updating task status: ${error}`);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Tasks</h1>
          <p className="text-gray-600 mt-1">Manage and track individual tasks</p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={testConnection} variant="outline">
            Test DB
          </Button>
          <Button onClick={createTestData} variant="outline">
            Create Test Data
          </Button>
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            New Task
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search tasks..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Status: {statusFilter}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => setStatusFilter('All')}>All</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('To Do')}>To Do</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('In Progress')}>In Progress</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('Done')}>Done</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Tasks List */}
      <div className="space-y-4">
        {filteredTasks.map((task) => (
          <Card key={task.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleStatusChange(task.id, task.status === 'Done' ? 'To Do' : 'Done')}
                      className="p-0 h-6 w-6"
                    >
                      <CheckCircle className={`h-5 w-5 ${task.status === 'Done' ? 'text-green-500' : 'text-gray-300'}`} />
                    </Button>
                    <h3 className={`font-semibold text-lg ${task.status === 'Done' ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                      {task.title}
                    </h3>
                    <Badge className={getStatusColor(task.status)}>
                      {task.status}
                    </Badge>
                    <Badge className={getPriorityColor(task.priority)}>
                      {task.priority}
                    </Badge>
                  </div>
                  
                  <p className="text-gray-600 mb-3">{task.description || 'No description available'}</p>
                  
                  <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-1" />
                      <Avatar className="h-6 w-6 mr-2">
                        <AvatarFallback className="text-xs">{task.assignee}</AvatarFallback>
                      </Avatar>
                      {task.assignee}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      Due {new Date(task.dueDate).toLocaleDateString()}
                    </div>
                    <div>Project: {task.project}</div>
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem 
                      onClick={() => {
                        setSelectedTask(task);
                        setEditDialogOpen(true);
                      }}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => handleDeleteTask(task.id)}
                      className="text-red-600"
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTasks.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">No tasks found</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || statusFilter !== 'All' 
              ? 'Try adjusting your search or filters' 
              : 'Get started by creating your first task'
            }
          </p>
          {!searchTerm && statusFilter === 'All' && (
            <Button onClick={() => setCreateDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Task
            </Button>
          )}
        </div>
      )}

      <CreateTaskDialog 
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onCreateTask={handleCreateTask}
      />

      {selectedTask && (
        <EditTaskDialog 
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          task={selectedTask}
          onEditTask={handleEditTask}
        />
      )}
    </div>
  );
};

export default Tasks;
