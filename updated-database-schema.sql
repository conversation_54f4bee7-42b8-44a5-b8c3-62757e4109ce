-- Updated database schema with user ownership and access codes

-- Create projects table with user ownership and access codes
CREATE TABLE IF NOT EXISTS projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'Planning',
  progress INTEGER NOT NULL DEFAULT 0,
  due_date TIMESTAMPTZ,
  team_members TEXT[], -- Array of user emails or IDs
  created_at TIMESTAMPTZ DEFAULT NOW(),
  owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  access_code TEXT UNIQUE -- Unique code for sharing projects
);

-- Create users table if it doesn't exist (for local user management)
CREATE TABLE IF NOT EXISTS users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT UNIQUE,
  name TEXT,
  role TEXT DEFAULT 'user',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- <PERSON>reate tasks table if it doesn't exist
CREATE TABLE IF NOT EXISTS tasks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT,
  description TEXT,
  status TEXT,
  due_date TIMESTAMPTZ,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  assigned_to UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Function to generate random access code
CREATE OR REPLACE FUNCTION generate_access_code() RETURNS TEXT AS $$
BEGIN
  RETURN upper(substring(md5(random()::text) from 1 for 8));
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically generate access code for new projects
CREATE OR REPLACE FUNCTION set_project_access_code()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.access_code IS NULL THEN
    NEW.access_code := generate_access_code();
    -- Ensure uniqueness
    WHILE EXISTS (SELECT 1 FROM projects WHERE access_code = NEW.access_code) LOOP
      NEW.access_code := generate_access_code();
    END LOOP;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for access code generation
DROP TRIGGER IF EXISTS trigger_set_project_access_code ON projects;
CREATE TRIGGER trigger_set_project_access_code
  BEFORE INSERT ON projects
  FOR EACH ROW
  EXECUTE FUNCTION set_project_access_code();

-- Enable Row Level Security
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for projects
DROP POLICY IF EXISTS "Users can view their own projects" ON projects;
DROP POLICY IF EXISTS "Users can view projects they are team members of" ON projects;
DROP POLICY IF EXISTS "Users can create their own projects" ON projects;
DROP POLICY IF EXISTS "Users can update their own projects" ON projects;
DROP POLICY IF EXISTS "Users can delete their own projects" ON projects;

-- Policy: Users can view projects they own
CREATE POLICY "Users can view their own projects" ON projects
  FOR SELECT USING (auth.uid() = owner_id);

-- Policy: Users can view projects where they are team members
CREATE POLICY "Users can view projects they are team members of" ON projects
  FOR SELECT USING (
    auth.jwt() ->> 'email' = ANY(team_members) OR
    auth.uid()::text = ANY(team_members)
  );

-- Policy: Users can create projects (they become the owner)
CREATE POLICY "Users can create their own projects" ON projects
  FOR INSERT WITH CHECK (auth.uid() = owner_id);

-- Policy: Users can update their own projects
CREATE POLICY "Users can update their own projects" ON projects
  FOR UPDATE USING (auth.uid() = owner_id);

-- Policy: Users can delete their own projects
CREATE POLICY "Users can delete their own projects" ON projects
  FOR DELETE USING (auth.uid() = owner_id);

-- Create RLS policies for tasks
DROP POLICY IF EXISTS "Users can view tasks in their projects" ON tasks;
DROP POLICY IF EXISTS "Users can create tasks in their projects" ON tasks;
DROP POLICY IF EXISTS "Users can update tasks in their projects" ON tasks;
DROP POLICY IF EXISTS "Users can delete tasks in their projects" ON tasks;

-- Policy: Users can view tasks in projects they have access to
CREATE POLICY "Users can view tasks in their projects" ON tasks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = tasks.project_id 
      AND (
        projects.owner_id = auth.uid() OR 
        auth.jwt() ->> 'email' = ANY(projects.team_members) OR
        auth.uid()::text = ANY(projects.team_members)
      )
    )
  );

-- Policy: Users can create tasks in projects they have access to
CREATE POLICY "Users can create tasks in their projects" ON tasks
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = tasks.project_id 
      AND (
        projects.owner_id = auth.uid() OR 
        auth.jwt() ->> 'email' = ANY(projects.team_members) OR
        auth.uid()::text = ANY(projects.team_members)
      )
    )
  );

-- Policy: Users can update tasks in projects they have access to
CREATE POLICY "Users can update tasks in their projects" ON tasks
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = tasks.project_id 
      AND (
        projects.owner_id = auth.uid() OR 
        auth.jwt() ->> 'email' = ANY(projects.team_members) OR
        auth.uid()::text = ANY(projects.team_members)
      )
    )
  );

-- Policy: Users can delete tasks in projects they have access to
CREATE POLICY "Users can delete tasks in their projects" ON tasks
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = tasks.project_id 
      AND (
        projects.owner_id = auth.uid() OR 
        auth.jwt() ->> 'email' = ANY(projects.team_members) OR
        auth.uid()::text = ANY(projects.team_members)
      )
    )
  );

-- Function to join project by access code
CREATE OR REPLACE FUNCTION join_project_by_code(access_code_param TEXT)
RETURNS JSON AS $$
DECLARE
  project_record RECORD;
  user_email TEXT;
  updated_members TEXT[];
BEGIN
  -- Get user email from JWT
  user_email := auth.jwt() ->> 'email';
  
  IF user_email IS NULL THEN
    RETURN json_build_object('success', false, 'message', 'User not authenticated');
  END IF;
  
  -- Find project by access code
  SELECT * INTO project_record FROM projects WHERE access_code = access_code_param;
  
  IF NOT FOUND THEN
    RETURN json_build_object('success', false, 'message', 'Invalid access code');
  END IF;
  
  -- Check if user is already a member
  IF user_email = ANY(project_record.team_members) THEN
    RETURN json_build_object('success', false, 'message', 'You are already a member of this project');
  END IF;
  
  -- Add user to team members
  updated_members := array_append(project_record.team_members, user_email);
  
  -- Update project
  UPDATE projects 
  SET team_members = updated_members 
  WHERE id = project_record.id;
  
  RETURN json_build_object(
    'success', true, 
    'message', 'Successfully joined project',
    'project_name', project_record.name
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
